// Direct test of signup functionality using Supabase client
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://dpicnvisvxpmgjtbeicf.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM3OTE0MDgsImV4cCI6MjA1OTM2NzQwOH0.6x_KSp-mDrLNqZiSW3ZB4VFDPM5tQas0mc68Md-Uvrc'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testSignup() {
  const testEmail = `test.signup.${Date.now()}@example.com`
  const testPassword = 'TestPassword123!'
  
  console.log('Testing signup with:', { email: testEmail, password: '***' })
  
  try {
    const { data, error } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        emailRedirectTo: 'http://localhost:5173/auth/verify'
      }
    })
    
    if (error) {
      console.error('Signup error:', error)
      return { success: false, error: error.message }
    } else {
      console.log('Signup success:', data)
      return { success: true, user: data.user }
    }
  } catch (err) {
    console.error('Unexpected error:', err)
    return { success: false, error: err.message }
  }
}

// Run the test
testSignup().then(result => {
  console.log('Test result:', result)
}).catch(err => {
  console.error('Test failed:', err)
})
