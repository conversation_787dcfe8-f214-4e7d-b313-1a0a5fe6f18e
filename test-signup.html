<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Signup</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <h1>Test Signup Functionality</h1>
    
    <form id="signupForm">
        <div>
            <label for="email">Email:</label>
            <input type="email" id="email" required>
        </div>
        <div>
            <label for="password">Password:</label>
            <input type="password" id="password" required>
        </div>
        <button type="submit">Sign Up</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        // Initialize Supabase client
        const supabaseUrl = 'https://dpicnvisvxpmgjtbeicf.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM3OTE0MDgsImV4cCI6MjA1OTM2NzQwOH0.6x_KSp-mDrLNqZiSW3ZB4VFDPM5tQas0mc68Md-Uvrc';
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);
        
        document.getElementById('signupForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = 'Testing signup...';
            
            try {
                console.log('Attempting signup with:', { email, password: '***' });
                
                const { data, error } = await supabase.auth.signUp({
                    email: email,
                    password: password,
                    options: {
                        emailRedirectTo: `${window.location.origin}/auth/verify`
                    }
                });
                
                if (error) {
                    console.error('Signup error:', error);
                    resultDiv.innerHTML = `<div style="color: red;">Error: ${error.message}</div>`;
                } else {
                    console.log('Signup success:', data);
                    resultDiv.innerHTML = `<div style="color: green;">Success! User created: ${data.user?.email}</div>`;
                }
            } catch (err) {
                console.error('Unexpected error:', err);
                resultDiv.innerHTML = `<div style="color: red;">Unexpected error: ${err.message}</div>`;
            }
        });
    </script>
</body>
</html>
